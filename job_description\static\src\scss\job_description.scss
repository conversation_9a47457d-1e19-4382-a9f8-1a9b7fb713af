/* Job Description Module Styles */

.o_form_view {
    .o_notebook {
        .tab-pane[name="job_description"] {
            .alert-info {
                background-color: #e8f4fd;
                border-color: #b8daff;
                color: #0c5460;
                margin-bottom: 20px;
                
                strong {
                    color: #0c5460;
                }
            }
            
            .o_group {
                .o_form_label {
                    font-weight: 600;
                    color: #495057;
                }
                
                .o_field_text {
                    textarea {
                        min-height: 120px;
                        border-radius: 4px;
                        border: 1px solid #ced4da;
                        padding: 10px;
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                        
                        &:focus {
                            border-color: #875a7b;
                            box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.25);
                        }
                        
                        &::placeholder {
                            color: #6c757d;
                            font-style: italic;
                        }
                    }
                }
                
                .o_field_many2one {
                    .o_input {
                        border-radius: 4px;
                        
                        &:focus {
                            border-color: #875a7b;
                            box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.25);
                        }
                    }
                }
            }
            
            // Group styling
            .o_group[name="job_details"] {
                .o_horizontal_separator {
                    color: #875a7b;
                    font-weight: 600;
                    border-bottom: 2px solid #875a7b;
                    margin-bottom: 15px;
                }
            }
            
            .o_group[name="job_requirements"] {
                .o_horizontal_separator {
                    color: #28a745;
                    font-weight: 600;
                    border-bottom: 2px solid #28a745;
                    margin-bottom: 15px;
                }
            }
        }
    }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
    .o_form_view .o_notebook .tab-pane[name="job_description"] {
        .row .col-12 {
            padding: 0 10px;
        }
        
        .o_group .o_field_text textarea {
            min-height: 100px;
        }
    }
}

// Print styles
@media print {
    .o_form_view .o_notebook .tab-pane[name="job_description"] {
        .alert-info {
            display: none;
        }
        
        .o_group {
            page-break-inside: avoid;
            
            .o_form_label {
                font-weight: bold;
                color: #000;
            }
        }
    }
}
