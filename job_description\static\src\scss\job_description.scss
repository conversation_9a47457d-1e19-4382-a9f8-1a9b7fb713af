/* Job Description Module Styles */

.o_form_view {
    .o_notebook {
        .tab-pane[name="job_description"] {
            .alert-info {
                background-color: #e8f4fd;
                border-color: #b8daff;
                color: #0c5460;
                margin-bottom: 20px;
                
                strong {
                    color: #0c5460;
                }
            }
            
            .o_group {
                .o_form_label {
                    font-weight: 600;
                    color: #495057;
                }
                
                .o_field_text {
                    textarea {
                        min-height: 120px;
                        border-radius: 4px;
                        border: 1px solid #ced4da;
                        padding: 10px;
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                        
                        &:focus {
                            border-color: #875a7b;
                            box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.25);
                        }
                        
                        &::placeholder {
                            color: #6c757d;
                            font-style: italic;
                        }
                    }
                }
                
                .o_field_many2one {
                    .o_input {
                        border-radius: 4px;
                        
                        &:focus {
                            border-color: #875a7b;
                            box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.25);
                        }
                    }
                }
            }
            
            // Group styling
            .o_group[name="job_details"] {
                .o_horizontal_separator {
                    color: #875a7b;
                    font-weight: 600;
                    border-bottom: 2px solid #875a7b;
                    margin-bottom: 15px;
                }
            }
            
            .o_group[name="job_requirements"] {
                .o_horizontal_separator {
                    color: #28a745;
                    font-weight: 600;
                    border-bottom: 2px solid #28a745;
                    margin-bottom: 15px;
                }
            }
        }
    }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
    .o_form_view .o_notebook .tab-pane[name="job_description"] {
        .row .col-12 {
            padding: 0 10px;
        }
        
        .o_group .o_field_text textarea {
            min-height: 100px;
        }
    }
}

// HR Settings Job Description Display Styles
.o_settings_container {
    #job_description_display_setting {
        .job_description_readonly {
            textarea {
                background-color: #f8f9fa !important;
                border: 1px solid #e9ecef !important;
                color: #495057 !important;
                min-height: 100px;
                resize: none;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                font-size: 14px;
                line-height: 1.4;

                &:focus {
                    box-shadow: none !important;
                    border-color: #e9ecef !important;
                }
            }
        }

        .alert-info {
            background-color: #e8f4fd;
            border-color: #b8daff;
            color: #0c5460;
            margin-bottom: 20px;
            border-radius: 6px;

            strong {
                color: #0c5460;
                font-weight: 600;
            }
        }

        .o_form_label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .o_field_char,
        .o_field_integer {
            input {
                background-color: #f8f9fa !important;
                border: 1px solid #e9ecef !important;
                color: #495057 !important;

                &:focus {
                    box-shadow: none !important;
                    border-color: #e9ecef !important;
                }
            }
        }

        .btn-link {
            color: #875a7b;
            text-decoration: none;

            &:hover {
                color: #6b4c57;
                text-decoration: underline;
            }

            i {
                margin-right: 5px;
            }
        }

        .o_field_many2one {
            .o_input {
                border-radius: 4px;

                &:focus {
                    border-color: #875a7b;
                    box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.25);
                }
            }
        }
    }
}

// Print styles
@media print {
    .o_form_view .o_notebook .tab-pane[name="job_description"] {
        .alert-info {
            display: none;
        }

        .o_group {
            page-break-inside: avoid;

            .o_form_label {
                font-weight: bold;
                color: #000;
            }
        }
    }

    .o_settings_container #job_description_display_setting {
        .alert-info {
            display: none;
        }

        .btn-link {
            display: none;
        }
    }
}
