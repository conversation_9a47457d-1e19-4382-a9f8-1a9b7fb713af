# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # Job Position Selection for displaying job description
    selected_job_position_id = fields.Many2one(
        'hr.job',
        string='Job Position',
        help='Select a job position to view its detailed description'
    )
    
    # Job Description Display Fields (readonly)
    display_job_objective = fields.Text(
        string='Job Objective',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        help='Displays the job objective for the selected position'
    )

    display_main_tasks = fields.Text(
        string='Main Tasks',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        help='Displays the main tasks for the selected position'
    )

    display_reporting_to = fields.Char(
        string='Reporting To',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        help='Displays the reporting hierarchy for the selected position'
    )

    display_required_skills = fields.Text(
        string='Required Skills',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        help='Displays the required skills for the selected position'
    )

    # Additional computed fields for better display
    display_job_name = fields.Char(
        string='Position Name',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False
    )

    display_department = fields.Char(
        string='Department',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False
    )

    display_employees_count = fields.Integer(
        string='Current Employees',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        default=0
    )

    @api.depends('selected_job_position_id')
    def _compute_job_description_fields(self):
        """Compute job description fields based on selected job position"""
        for record in self:
            try:
                if record.selected_job_position_id:
                    job = record.selected_job_position_id

                    # Set job description fields with safe string handling
                    record.display_job_objective = job.job_objective or _('No job objective defined')
                    record.display_main_tasks = job.main_tasks or _('No main tasks defined')
                    record.display_required_skills = job.required_skills or _('No required skills defined')

                    # Set reporting hierarchy with safe handling
                    if job.reporting_to:
                        record.display_reporting_to = job.reporting_to.name or _('Unnamed position')
                    else:
                        record.display_reporting_to = _('No reporting hierarchy defined')

                    # Set additional info with safe handling
                    record.display_job_name = job.name or _('Unnamed job position')
                    record.display_department = job.department_id.name if job.department_id else _('No department assigned')

                    # Safe handling of employee count - ensure it's always an integer
                    try:
                        # Force computation of the field if needed
                        if hasattr(job, '_compute_employees'):
                            job._compute_employees()

                        employee_count = job.no_of_employee
                        if employee_count is None or employee_count == '' or employee_count is False:
                            record.display_employees_count = 0
                        else:
                            record.display_employees_count = int(employee_count)
                    except (ValueError, TypeError, AttributeError) as e:
                        import logging
                        _logger = logging.getLogger(__name__)
                        _logger.debug(f"Error getting employee count for job {job.name}: {str(e)}")
                        record.display_employees_count = 0

                else:
                    # Clear all fields when no job is selected
                    record.display_job_objective = False
                    record.display_main_tasks = False
                    record.display_reporting_to = False
                    record.display_required_skills = False
                    record.display_job_name = False
                    record.display_department = False
                    record.display_employees_count = 0

            except Exception as e:
                # Log the error and set default values
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning(f"Error computing job description fields: {str(e)}")

                # Set safe default values
                record.display_job_objective = False
                record.display_main_tasks = False
                record.display_reporting_to = False
                record.display_required_skills = False
                record.display_job_name = False
                record.display_department = False
                record.display_employees_count = 0
