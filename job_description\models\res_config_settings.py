# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # Job Position Selection for displaying job description
    selected_job_position_id = fields.Many2one(
        'hr.job',
        string='Job Position',
        help='Select a job position to view its detailed description'
    )
    
    # Job Description Display Fields (readonly)
    display_job_objective = fields.Text(
        string='Job Objective',
        readonly=True,
        compute='_compute_job_description_fields',
        help='Displays the job objective for the selected position'
    )
    
    display_main_tasks = fields.Text(
        string='Main Tasks',
        readonly=True,
        compute='_compute_job_description_fields',
        help='Displays the main tasks for the selected position'
    )
    
    display_reporting_to = fields.Char(
        string='Reporting To',
        readonly=True,
        compute='_compute_job_description_fields',
        help='Displays the reporting hierarchy for the selected position'
    )
    
    display_required_skills = fields.Text(
        string='Required Skills',
        readonly=True,
        compute='_compute_job_description_fields',
        help='Displays the required skills for the selected position'
    )
    
    # Additional computed fields for better display
    display_job_name = fields.Char(
        string='Position Name',
        readonly=True,
        compute='_compute_job_description_fields'
    )
    
    display_department = fields.Char(
        string='Department',
        readonly=True,
        compute='_compute_job_description_fields'
    )
    
    display_employees_count = fields.Integer(
        string='Current Employees',
        readonly=True,
        compute='_compute_job_description_fields'
    )

    @api.depends('selected_job_position_id')
    def _compute_job_description_fields(self):
        """Compute job description fields based on selected job position"""
        for record in self:
            if record.selected_job_position_id:
                job = record.selected_job_position_id
                
                # Set job description fields
                record.display_job_objective = job.job_objective or _('No job objective defined')
                record.display_main_tasks = job.main_tasks or _('No main tasks defined')
                record.display_required_skills = job.required_skills or _('No required skills defined')
                
                # Set reporting hierarchy
                if job.reporting_to:
                    record.display_reporting_to = job.reporting_to.name
                else:
                    record.display_reporting_to = _('No reporting hierarchy defined')
                
                # Set additional info
                record.display_job_name = job.name
                record.display_department = job.department_id.name if job.department_id else _('No department assigned')
                record.display_employees_count = job.no_of_employee
                
            else:
                # Clear all fields when no job is selected
                record.display_job_objective = ''
                record.display_main_tasks = ''
                record.display_reporting_to = ''
                record.display_required_skills = ''
                record.display_job_name = ''
                record.display_department = ''
                record.display_employees_count = 0
