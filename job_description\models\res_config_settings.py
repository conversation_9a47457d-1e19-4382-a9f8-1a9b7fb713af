# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
import logging
from functools import wraps

_logger = logging.getLogger(__name__)


def safe_compute(default_values=None):
    """Decorator to safely handle computed field errors"""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                _logger.error(f"Error in {func.__name__}: {str(e)}", exc_info=True)
                # Set safe default values if provided
                if default_values:
                    for record in self:
                        for field, value in default_values.items():
                            setattr(record, field, value)
                return None
        return wrapper
    return decorator


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # Job Position Selection for displaying job description
    selected_job_position_id = fields.Many2one(
        'hr.job',
        string='Job Position',
        help='Select a job position to view its detailed description'
    )
    
    # Job Description Display Fields (readonly)
    display_job_objective = fields.Text(
        string='Job Objective',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        help='Displays the job objective for the selected position'
    )

    display_main_tasks = fields.Text(
        string='Main Tasks',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        help='Displays the main tasks for the selected position'
    )

    display_reporting_to = fields.Char(
        string='Reporting To',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        help='Displays the reporting hierarchy for the selected position'
    )

    display_required_skills = fields.Text(
        string='Required Skills',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        help='Displays the required skills for the selected position'
    )

    # Additional computed fields for better display
    display_job_name = fields.Char(
        string='Position Name',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False
    )

    display_department = fields.Char(
        string='Department',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False
    )

    display_employees_count = fields.Integer(
        string='Current Employees',
        readonly=True,
        compute='_compute_job_description_fields',
        store=False,
        default=0,
        help='Number of employees currently in this position'
    )

    @api.depends('selected_job_position_id')
    @safe_compute(default_values={
        'display_job_objective': False,
        'display_main_tasks': False,
        'display_reporting_to': False,
        'display_required_skills': False,
        'display_job_name': False,
        'display_department': False,
        'display_employees_count': 0,
    })
    def _compute_job_description_fields(self):
        """Compute job description fields based on selected job position"""
        for record in self:
            # Initialize all fields with safe default values first
            record.display_job_objective = False
            record.display_main_tasks = False
            record.display_reporting_to = False
            record.display_required_skills = False
            record.display_job_name = False
            record.display_department = False
            record.display_employees_count = 0

            try:
                if record.selected_job_position_id:
                    job = record.selected_job_position_id
                    _logger.debug(f"Computing job description fields for job: {job.name}")

                    # Set job description fields with safe string handling
                    record.display_job_objective = self._safe_get_text_field(job, 'job_objective', _('No job objective defined'))
                    record.display_main_tasks = self._safe_get_text_field(job, 'main_tasks', _('No main tasks defined'))
                    record.display_required_skills = self._safe_get_text_field(job, 'required_skills', _('No required skills defined'))

                    # Set reporting hierarchy with safe handling
                    record.display_reporting_to = self._safe_get_reporting_to(job)

                    # Set additional info with safe handling
                    record.display_job_name = self._safe_get_text_field(job, 'name', _('Unnamed job position'))
                    record.display_department = self._safe_get_department(job)

                    # Safe handling of employee count - ensure it's always an integer
                    record.display_employees_count = self._safe_get_employee_count(job)

            except Exception as e:
                # Log the error and keep default values
                _logger.error(f"Error computing job description fields: {str(e)}", exc_info=True)
                # Default values already set above

    def _safe_get_text_field(self, job, field_name, default_value):
        """Safely get text field value with fallback"""
        try:
            value = getattr(job, field_name, None)
            if value and str(value).strip():
                return str(value).strip()
            return default_value
        except Exception as e:
            _logger.debug(f"Error getting field {field_name}: {str(e)}")
            return default_value

    def _safe_get_reporting_to(self, job):
        """Safely get reporting hierarchy"""
        try:
            if hasattr(job, 'reporting_to') and job.reporting_to:
                reporting_name = getattr(job.reporting_to, 'name', None)
                if reporting_name and str(reporting_name).strip():
                    return str(reporting_name).strip()
            return _('No reporting hierarchy defined')
        except Exception as e:
            _logger.debug(f"Error getting reporting_to: {str(e)}")
            return _('No reporting hierarchy defined')

    def _safe_get_department(self, job):
        """Safely get department name"""
        try:
            if hasattr(job, 'department_id') and job.department_id:
                dept_name = getattr(job.department_id, 'name', None)
                if dept_name and str(dept_name).strip():
                    return str(dept_name).strip()
            return _('No department assigned')
        except Exception as e:
            _logger.debug(f"Error getting department: {str(e)}")
            return _('No department assigned')

    def _safe_get_employee_count(self, job):
        """Safely get employee count as integer"""
        try:
            # Try to get the computed field value
            employee_count = getattr(job, 'no_of_employee', None)
            _logger.debug(f"Raw employee_count value: {repr(employee_count)} (type: {type(employee_count)})")

            # Handle various types of empty/invalid values
            if employee_count is None:
                _logger.debug("employee_count is None, returning 0")
                return 0
            if employee_count == '':
                _logger.debug("employee_count is empty string, returning 0")
                return 0
            if employee_count is False:
                _logger.debug("employee_count is False, returning 0")
                return 0
            if str(employee_count).strip() == '':
                _logger.debug("employee_count is whitespace, returning 0")
                return 0

            # Try to convert to int with multiple fallback methods
            conversion_methods = [
                lambda x: int(x),
                lambda x: int(float(x)),
                lambda x: int(float(str(x))),
                lambda x: int(str(x).strip()),
                lambda x: int(float(str(x).strip())),
            ]

            for method in conversion_methods:
                try:
                    result = method(employee_count)
                    _logger.debug(f"Successfully converted {repr(employee_count)} to {result}")
                    return max(0, result)  # Ensure non-negative
                except (ValueError, TypeError) as e:
                    _logger.debug(f"Conversion method failed: {str(e)}")
                    continue

            # If all conversion methods fail, try to force computation
            _logger.debug("All conversion methods failed, trying to force computation")
            if hasattr(job, '_compute_employees'):
                try:
                    job._compute_employees()
                    employee_count = getattr(job, 'no_of_employee', 0)
                    _logger.debug(f"After computation: {repr(employee_count)}")

                    if employee_count is not None and str(employee_count).strip():
                        for method in conversion_methods:
                            try:
                                result = method(employee_count)
                                return max(0, result)
                            except (ValueError, TypeError):
                                continue
                except Exception as e:
                    _logger.debug(f"Error during computation: {str(e)}")

            _logger.warning(f"Could not convert employee_count {repr(employee_count)} to integer, returning 0")
            return 0

        except Exception as e:
            _logger.error(f"Critical error getting employee count for job {getattr(job, 'name', 'Unknown')}: {str(e)}", exc_info=True)
            return 0
