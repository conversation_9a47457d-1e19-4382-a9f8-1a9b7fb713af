# دليل المستخدم - موديول وصف الوظيفة

## نظرة عامة
يتيح لك هذا الموديول إضافة معلومات مفصلة عن كل وظيفة في نظام الموارد البشرية، مما يساعد في توضيح متطلبات ومسؤوليات كل منصب وظيفي.

## طرق الوصول إلى الوظائف

### الطريقة الأولى: إدارة الوظائف (للتعديل)

#### الخطوة 1: الانتقال إلى قائمة الوظائف
1. اذهب إلى القائمة الرئيسية
2. انقر على **"الموظفون"** (Employees)
3. اختر **"التكوين"** (Configuration)
4. انقر على **"المناصب الوظيفية"** (Job Positions)

#### الخطوة 2: فتح وظيفة موجودة أو إنشاء جديدة
- **لفتح وظيفة موجودة**: انقر على اسم الوظيفة من القائمة
- **لإنشاء وظيفة جديدة**: انقر على زر **"إنشاء"** (Create)

### الطريقة الثانية: عرض الوظائف في الإعدادات (للقراءة فقط)

#### الخطوة 1: الانتقال إلى إعدادات الموارد البشرية
1. اذهب إلى القائمة الرئيسية
2. انقر على **"الإعدادات"** (Settings)
3. اختر **"الموارد البشرية"** (Human Resources)

#### الخطوة 2: عرض تفاصيل الوظيفة
1. ابحث عن قسم **"تفاصيل المنصب الوظيفي"** (Job Position Details)
2. اختر وظيفة من القائمة المنسدلة
3. ستظهر جميع تفاصيل الوظيفة تلقائياً (للقراءة فقط)

## استخدام ميزات وصف الوظيفة

### الطريقة الأولى: تبويب وصف الوظيفة (للتعديل)

#### العثور على التبويب
بعد فتح أي وظيفة، ستجد تبويب جديد بعنوان **"Job Description"** أو **"وصف الوظيفة"**

### الطريقة الثانية: عرض في إعدادات الموارد البشرية (للقراءة فقط)

#### مميزات العرض في الإعدادات:
- **سهولة الوصول**: لا حاجة للدخول إلى كل وظيفة على حدة
- **عرض سريع**: اختيار الوظيفة يعرض التفاصيل فوراً
- **للقراءة فقط**: لا يمكن التعديل عن طريق الخطأ
- **أزرار سريعة**: للانتقال إلى التعديل أو عرض جميع الوظائف

#### كيفية الاستخدام:
1. اذهب إلى **الإعدادات > الموارد البشرية**
2. ابحث عن قسم **"تفاصيل المنصب الوظيفي"**
3. اختر وظيفة من القائمة المنسدلة **"اختر المنصب الوظيفي"**
4. ستظهر المعلومات التالية تلقائياً:
   - **اسم المنصب**: اسم الوظيفة المحددة
   - **القسم**: القسم التابع له المنصب
   - **يرفع إلى**: المنصب الذي يرفع إليه
   - **الموظفون الحاليون**: عدد الموظفين في هذا المنصب
   - **هدف الوظيفة**: الهدف العام للمنصب
   - **المهام الرئيسية**: قائمة بالمهام والمسؤوليات
   - **المهارات المطلوبة**: المؤهلات والخبرات المطلوبة

#### الأزرار المتاحة:
- **تعديل المنصب الوظيفي**: ينقلك مباشرة لتعديل الوظيفة المحددة
- **عرض جميع المناصب الوظيفية**: يفتح قائمة جميع الوظائف

### الحقول المتاحة

#### 1. هدف الوظيفة (Job Objective)
- **الغرض**: وصف الهدف العام والغرض من هذا المنصب
- **مثال**: "قيادة فريق المبيعات لتحقيق أهداف الإيرادات وضمان رضا العملاء"
- **نصائح**: 
  - اكتب هدف واضح ومحدد
  - استخدم لغة بسيطة ومفهومة
  - ركز على النتائج المتوقعة

#### 2. المهام الرئيسية (Main Tasks)
- **الغرض**: قائمة بالمهام والمسؤوليات الأساسية
- **مثال**: 
  ```
  • إدارة فريق المبيعات وتوجيههم
  • وضع استراتيجيات المبيعات الشهرية
  • متابعة أداء الفريق وتقييمه
  • التواصل مع العملاء الرئيسيين
  ```
- **نصائح**:
  - استخدم نقاط منفصلة لكل مهمة
  - ابدأ كل نقطة بفعل واضح
  - رتب المهام حسب الأهمية

#### 3. يرفع إلى (Reporting To)
- **الغرض**: تحديد المنصب الذي يرفع إليه هذا الدور
- **كيفية الاستخدام**: 
  - انقر على الحقل
  - اختر من قائمة الوظائف الموجودة
  - أو ابحث عن اسم الوظيفة
- **فائدة**: يساعد في رسم الهيكل التنظيمي

#### 4. المهارات المطلوبة (Required Skills)
- **الغرض**: وصف المهارات والمؤهلات المطلوبة
- **مثال**:
  ```
  • شهادة جامعية في إدارة الأعمال
  • خبرة 5 سنوات في المبيعات
  • مهارات قيادية قوية
  • إجادة اللغة الإنجليزية
  • خبرة في استخدام أنظمة CRM
  ```
- **نصائح**:
  - قسم المتطلبات إلى: تعليم، خبرة، مهارات
  - حدد المتطلبات الأساسية والمفضلة
  - كن واقعياً في التوقعات

## أمثلة عملية

### مثال 1: مدير مبيعات
- **هدف الوظيفة**: "قيادة فريق المبيعات لتحقيق أهداف الإيرادات الشهرية وبناء علاقات قوية مع العملاء"
- **المهام الرئيسية**: 
  - إدارة فريق من 8 مندوبي مبيعات
  - وضع خطط المبيعات الشهرية والسنوية
  - متابعة أداء الفريق وتقديم التدريب
- **يرفع إلى**: مدير عام المبيعات
- **المهارات المطلوبة**: شهادة جامعية، 5+ سنوات خبرة، مهارات قيادية

### مثال 2: مطور برمجيات
- **هدف الوظيفة**: "تطوير وصيانة التطبيقات البرمجية باستخدام أحدث التقنيات"
- **المهام الرئيسية**:
  - كتابة كود نظيف وقابل للصيانة
  - المشاركة في مراجعة الكود
  - التعاون مع فريق التصميم
- **يرفع إلى**: رئيس المطورين
- **المهارات المطلوبة**: شهادة في علوم الحاسوب، خبرة في Python وJavaScript

## نصائح للاستخدام الأمثل

### 1. الاتساق
- استخدم نفس التنسيق لجميع الوظائف
- حافظ على طول متشابه للأوصاف
- استخدم مصطلحات موحدة

### 2. التحديث المستمر
- راجع أوصاف الوظائف دورياً
- حدث المتطلبات حسب تطور الشركة
- اطلب ملاحظات من الموظفين الحاليين

### 3. الوضوح
- تجنب المصطلحات المعقدة
- استخدم جمل قصيرة ومباشرة
- أعط أمثلة عملية عند الحاجة

### 4. الشمولية
- لا تترك حقول فارغة إذا أمكن
- اذكر جميع المتطلبات المهمة
- وضح التوقعات بوضوح

## استكشاف الأخطاء

### المشكلة: لا أرى تبويب "وصف الوظيفة"
**الحلول**:
- تأكد من تثبيت الموديول
- أعد تحميل الصفحة (F5)
- تحقق من صلاحياتك

### المشكلة: لا أستطيع حفظ التغييرات
**الحلول**:
- تأكد من صلاحيات التعديل
- تحقق من اتصال الإنترنت
- جرب إعادة تسجيل الدخول

### المشكلة: الحقول تظهر باللغة الإنجليزية
**الحلول**:
- تحقق من إعدادات اللغة في حسابك
- اطلب من المدير تحديث ملفات الترجمة

## الدعم الفني
للحصول على مساعدة إضافية:
1. راجع دليل المطور (DEVELOPER_GUIDE.md)
2. تحقق من ملف الأسئلة الشائعة
3. اتصل بفريق الدعم الفني
