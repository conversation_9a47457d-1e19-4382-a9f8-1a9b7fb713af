#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug helper for job_description module
This script helps diagnose ValueError issues in HR Settings
"""

import logging

def debug_job_description_fields():
    """Debug function to test job description field computation"""
    print("=== Job Description Debug Helper ===")
    
    try:
        # This would be run in Odoo shell context
        # odoo-bin shell -d your_database --addons-path=/path/to/addons
        
        print("1. Testing hr.job model...")
        jobs = env['hr.job'].search([])
        print(f"Found {len(jobs)} job positions")
        
        for job in jobs[:5]:  # Test first 5 jobs
            print(f"\nTesting job: {job.name}")
            print(f"  - ID: {job.id}")
            
            # Test no_of_employee field
            try:
                employee_count = job.no_of_employee
                print(f"  - no_of_employee: {employee_count} (type: {type(employee_count)})")
                
                # Test conversion to int
                if employee_count is None:
                    int_count = 0
                elif employee_count == '':
                    int_count = 0
                elif employee_count is False:
                    int_count = 0
                else:
                    int_count = int(employee_count)
                print(f"  - Converted to int: {int_count}")
                
            except Exception as e:
                print(f"  - ERROR with no_of_employee: {str(e)}")
                
            # Test job description fields
            try:
                print(f"  - job_objective: {bool(job.job_objective)}")
                print(f"  - main_tasks: {bool(job.main_tasks)}")
                print(f"  - required_skills: {bool(job.required_skills)}")
                print(f"  - reporting_to: {job.reporting_to.name if job.reporting_to else 'None'}")
            except Exception as e:
                print(f"  - ERROR with job description fields: {str(e)}")
        
        print("\n2. Testing res.config.settings...")
        
        # Test creating settings record
        try:
            settings = env['res.config.settings'].create({})
            print("  - Created empty settings record")
            
            if jobs:
                test_job = jobs[0]
                print(f"  - Testing with job: {test_job.name}")
                
                settings.selected_job_position_id = test_job.id
                settings._compute_job_description_fields()
                
                print(f"  - display_employees_count: {settings.display_employees_count} (type: {type(settings.display_employees_count)})")
                print(f"  - display_job_name: {settings.display_job_name}")
                print(f"  - display_department: {settings.display_department}")
                
        except Exception as e:
            print(f"  - ERROR with settings: {str(e)}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"CRITICAL ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

def test_problematic_values():
    """Test specific problematic values that might cause ValueError"""
    print("\n=== Testing Problematic Values ===")
    
    problematic_values = [
        '', None, False, 'invalid', '  ', 0, '0', 0.0, '0.0', 
        'null', 'undefined', [], {}, 'NaN', float('nan')
    ]
    
    for value in problematic_values:
        print(f"\nTesting value: {repr(value)} (type: {type(value)})")
        
        try:
            # Test the conversion logic
            if value is None:
                result = 0
            elif value == '':
                result = 0
            elif value is False:
                result = 0
            else:
                result = int(float(str(value)))
            print(f"  - Conversion successful: {result}")
            
        except ValueError as e:
            print(f"  - ValueError: {str(e)}")
        except Exception as e:
            print(f"  - Other error: {str(e)}")

if __name__ == '__main__':
    print("This script should be run in Odoo shell context:")
    print("odoo-bin shell -d your_database --addons-path=/path/to/addons")
    print("Then run: exec(open('job_description/debug_helper.py').read())")
    print("\nOr run individual functions:")
    print("debug_job_description_fields()")
    print("test_problematic_values()")
    
    # Run basic tests without Odoo context
    test_problematic_values()
