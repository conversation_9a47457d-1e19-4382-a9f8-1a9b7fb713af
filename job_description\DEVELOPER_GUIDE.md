# Developer Guide - Job Description Module

## Overview
This guide provides technical information for developers who want to understand, modify, or extend the Job Description module.

## Architecture

### Model Extension
The module extends the existing `hr.job` model using Odoo's inheritance mechanism:

```python
class HrJob(models.Model):
    _inherit = 'hr.job'
    
    # New fields are added here
```

### Field Definitions

#### job_objective
- **Type**: `fields.Text`
- **Purpose**: Store the general objective and purpose of the job
- **Widget**: `text` (multiline text input)

#### main_tasks
- **Type**: `fields.Text`
- **Purpose**: Store main tasks and responsibilities
- **Widget**: `text` (multiline text input)

#### reporting_to
- **Type**: `fields.Many2one('hr.job')`
- **Purpose**: Create hierarchical relationships between job positions
- **Options**: `{'no_create': True, 'no_create_edit': True}`

#### required_skills
- **Type**: `fields.Text`
- **Purpose**: Store required skills and qualifications
- **Widget**: `text` (multiline text input)

## View Inheritance

### Form View Extension
The module uses XPath to add a new tab to the existing job form:

```xml
<xpath expr="//notebook" position="inside">
    <page string="Job Description" name="job_description">
        <!-- New fields here -->
    </page>
</xpath>
```

### Layout Structure
- **Alert Box**: Informational message at the top
- **Two Groups**: 
  - Job Overview (job_objective, main_tasks)
  - Requirements & Hierarchy (reporting_to, required_skills)

## Styling

### SCSS Structure
The module includes custom SCSS for enhanced UI:
- Form field styling
- Focus states
- Responsive design
- Print styles

### Key CSS Classes
- `.tab-pane[name="job_description"]`: Main container
- `.o_field_text textarea`: Text field styling
- `.alert-info`: Information box styling

## Testing

### Unit Tests
Located in `tests/test_hr_job_description.py`:

1. **test_job_description_fields_exist**: Verifies fields can be created and updated
2. **test_reporting_to_field**: Tests Many2one relationship
3. **test_job_description_fields_optional**: Ensures fields are optional
4. **test_job_description_fields_text_type**: Tests long text handling

### Running Tests
```bash
# Run all tests for the module
odoo-bin -d your_database -i job_description --test-enable --stop-after-init

# Run specific test
odoo-bin -d your_database --test-tags job_description
```

## Extending the Module

### Adding New Fields
1. Add field definition in `models/hr_job.py`
2. Add field to view in `views/hr_job_views.xml`
3. Update translations in `i18n/ar.po`
4. Add tests for new field

Example:
```python
# In models/hr_job.py
job_benefits = fields.Text(
    string='Job Benefits',
    help='Describe the benefits and perks for this position'
)
```

```xml
<!-- In views/hr_job_views.xml -->
<field name="job_benefits" widget="text" placeholder="List benefits and perks..."/>
```

### Adding New Views
To add list/tree view columns:
```xml
<record id="view_hr_job_tree_inherit" model="ir.ui.view">
    <field name="name">hr.job.tree.inherit</field>
    <field name="model">hr.job</field>
    <field name="inherit_id" ref="hr.view_hr_job_tree"/>
    <field name="arch" type="xml">
        <field name="department_id" position="after">
            <field name="reporting_to"/>
        </field>
    </field>
</record>
```

### Adding Computed Fields
Example of a computed field:
```python
job_description_summary = fields.Char(
    string='Description Summary',
    compute='_compute_description_summary',
    store=True
)

@api.depends('job_objective', 'main_tasks')
def _compute_description_summary(self):
    for record in self:
        summary = ""
        if record.job_objective:
            summary = record.job_objective[:50] + "..."
        record.job_description_summary = summary
```

## Database Considerations

### Field Storage
- All text fields are stored as `TEXT` in PostgreSQL
- Many2one field creates foreign key constraint
- No additional indexes needed for basic functionality

### Migration
If adding new fields in updates:
```python
# In migrations/********.0/post-migration.py
def migrate(cr, version):
    # Add any data migration logic here
    pass
```

## Security

### Access Rights
The module inherits security from the base `hr.job` model:
- HR Officers can read/write
- HR Managers have full access
- Regular users have read access based on company rules

### Record Rules
No additional record rules needed as the module extends existing model.

## Performance Considerations

### Database Queries
- Text fields don't impact query performance significantly
- Many2one field adds minimal overhead
- Consider adding indexes if filtering by new fields frequently

### Memory Usage
- Text fields are loaded on demand
- No significant memory impact for normal usage

## Troubleshooting

### Common Issues

1. **Module not appearing in Apps list**
   - Check module path
   - Verify __manifest__.py syntax
   - Update apps list

2. **Fields not showing in form**
   - Check view inheritance
   - Verify field names match model
   - Clear browser cache

3. **Translation not working**
   - Update translation files
   - Restart Odoo server
   - Check language settings

### Debug Mode
Enable developer mode to:
- View field technical names
- Check view inheritance
- Debug JavaScript issues

## Contributing

### Code Style
- Follow Odoo coding standards
- Use meaningful variable names
- Add docstrings to methods
- Include type hints where appropriate

### Pull Requests
1. Create feature branch
2. Add tests for new functionality
3. Update documentation
4. Test on clean database
5. Submit pull request with description

## Resources

- [Odoo Developer Documentation](https://www.odoo.com/documentation/15.0/developer.html)
- [Odoo Model Reference](https://www.odoo.com/documentation/15.0/developer/reference/addons/orm.html)
- [Odoo View Architecture](https://www.odoo.com/documentation/15.0/developer/reference/addons/views.html)
