# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class HrJob(models.Model):
    _inherit = 'hr.job'

    # Job Description Fields
    job_objective = fields.Text(
        string='Job Objective',
        help='Describe the general objective and purpose of this job position'
    )
    
    main_tasks = fields.Text(
        string='Main Tasks',
        help='List the main tasks and responsibilities for this job position'
    )
    
    reporting_to = fields.Many2one(
        'hr.job',
        string='Reporting To',
        help='Select the job position that this role reports to'
    )
    
    required_skills = fields.Text(
        string='Required Skills',
        help='Describe the required skills, qualifications, and experience for this job position'
    )
