<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Inherit the hr.job form view to add Job Description tab -->
        <record id="view_hr_job_form_inherit_job_description" model="ir.ui.view">
            <field name="name">hr.job.form.inherit.job.description</field>
            <field name="model">hr.job</field>
            <field name="inherit_id" ref="hr.view_hr_job_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page string="Job Description" name="job_description">
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info" role="alert">
                                    <strong>Job Description Details</strong><br/>
                                    Use this section to provide comprehensive information about the job position, including objectives, tasks, and requirements.
                                </div>
                            </div>
                        </div>
                        <group>
                            <group name="job_details" string="Job Overview">
                                <field name="job_objective" widget="text" placeholder="Describe the main purpose and objectives of this position..."/>
                                <field name="main_tasks" widget="text" placeholder="List the key responsibilities and daily tasks..."/>
                            </group>
                            <group name="job_requirements" string="Requirements &amp; Hierarchy">
                                <field name="reporting_to" options="{'no_create': True, 'no_create_edit': True}" placeholder="Select the position this role reports to"/>
                                <field name="required_skills" widget="text" placeholder="Describe required skills, qualifications, and experience..."/>
                            </group>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
