<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Inherit the hr.job form view to add Job Description tab -->
        <record id="view_hr_job_form_inherit_job_description" model="ir.ui.view">
            <field name="name">hr.job.form.inherit.job.description</field>
            <field name="model">hr.job</field>
            <field name="inherit_id" ref="hr.view_hr_job_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page string="Job Description" name="job_description">
                        <group>
                            <group name="job_details">
                                <field name="job_objective" widget="text"/>
                                <field name="main_tasks" widget="text"/>
                            </group>
                            <group name="job_requirements">
                                <field name="reporting_to" options="{'no_create': True, 'no_create_edit': True}"/>
                                <field name="required_skills" widget="text"/>
                            </group>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
