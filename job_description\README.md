# Job Description Module

## Overview
This module extends the HR Job Position model in Odoo 15 Community Edition to include detailed job description fields.

## Features

### New Fields Added to hr.job Model:
1. **Job Objective** (`job_objective`) - Text field
   - Purpose: Describe the general objective and purpose of the job position
   
2. **Main Tasks** (`main_tasks`) - Text field  
   - Purpose: List the main tasks and responsibilities for the job position
   
3. **Reporting To** (`reporting_to`) - Many2one field (hr.job)
   - Purpose: Define the reporting hierarchy by linking to another job position
   
4. **Required Skills** (`required_skills`) - Text field
   - Purpose: Describe required skills, qualifications, and experience

### User Interface
- All new fields are displayed in a new **"Job Description"** tab in the Job Position form view
- Fields are organized in two groups for better layout:
  - Job Details: Job Objective, Main Tasks
  - Job Requirements: Reporting To, Required Skills

### HR Settings Integration
- **Job Position Details** section in HR Settings (Settings > Human Resources)
- Select any job position to view its complete description
- Read-only display of all job description fields
- Quick access buttons to edit positions or view all positions
- Real-time updates when job position is changed

## Installation

1. Copy the `job_description` folder to your Odoo addons directory
2. Update the apps list in Odoo
3. Install the "Job Description" module from Apps menu

## Usage

### Managing Job Descriptions
1. Go to **Employees > Configuration > Job Positions**
2. Open any existing job position or create a new one
3. Navigate to the **"Job Description"** tab
4. Fill in the new fields as needed:
   - **Job Objective**: Enter the main purpose of this role
   - **Main Tasks**: List key responsibilities and tasks
   - **Reporting To**: Select the job position this role reports to
   - **Required Skills**: Describe necessary qualifications and skills

### Viewing Job Descriptions in HR Settings
1. Go to **Settings > Human Resources**
2. Scroll down to **"Job Position Details"** section
3. Select a job position from the dropdown
4. View the complete job description (read-only)
5. Use action buttons to edit the position or view all positions

## Technical Details

### Dependencies
- `hr` (Human Resources module)

### Files Structure
```
job_description/
├── __init__.py
├── __manifest__.py
├── README.md
├── models/
│   ├── __init__.py
│   └── hr_job.py
└── views/
    └── hr_job_views.xml
```

### Model Extension
The module uses inheritance to extend the existing `hr.job` model without modifying core Odoo files.

### View Inheritance
The form view is extended using XPath to add a new tab to the existing Job Position form.

## Compatibility
- Odoo 15.0 Community Edition
- Compatible with other HR modules

## Author
Custom Development

## License
LGPL-3
