#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module validation script for job_description module
This script checks if the module structure is correct and all files are valid
"""

import os
import ast
import xml.etree.ElementTree as ET


def validate_python_file(filepath):
    """Validate Python file syntax"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        print(f"✓ {filepath} - Python syntax is valid")
        return True
    except SyntaxError as e:
        print(f"✗ {filepath} - Python syntax error: {e}")
        return False
    except Exception as e:
        print(f"✗ {filepath} - Error reading file: {e}")
        return False


def validate_xml_file(filepath):
    """Validate XML file syntax"""
    try:
        ET.parse(filepath)
        print(f"✓ {filepath} - XML syntax is valid")
        return True
    except ET.ParseError as e:
        print(f"✗ {filepath} - XML syntax error: {e}")
        return False
    except Exception as e:
        print(f"✗ {filepath} - Error reading file: {e}")
        return False


def validate_manifest(filepath):
    """Validate manifest file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse as Python dict
        manifest = ast.literal_eval(content.strip('# -*- coding: utf-8 -*-\n'))
        
        # Check required fields
        required_fields = ['name', 'version', 'depends', 'installable']
        for field in required_fields:
            if field not in manifest:
                print(f"✗ {filepath} - Missing required field: {field}")
                return False
        
        # Check if hr is in depends
        if 'hr' not in manifest['depends']:
            print(f"✗ {filepath} - 'hr' module not in depends")
            return False
            
        print(f"✓ {filepath} - Manifest is valid")
        return True
        
    except Exception as e:
        print(f"✗ {filepath} - Error validating manifest: {e}")
        return False


def main():
    """Main validation function"""
    print("Validating job_description module...")
    print("=" * 50)

    module_path = os.path.dirname(os.path.abspath(__file__))
    all_valid = True

    # Check module structure
    required_dirs = ['models', 'views', 'security', 'static', 'demo', 'tests', 'i18n']
    optional_dirs = ['static/src/scss', 'static/description']

    for dir_name in required_dirs:
        dir_path = os.path.join(module_path, dir_name)
        if os.path.exists(dir_path):
            print(f"✓ Directory {dir_name} exists")
        else:
            print(f"✗ Directory {dir_name} missing")
            all_valid = False

    for dir_name in optional_dirs:
        dir_path = os.path.join(module_path, dir_name)
        if os.path.exists(dir_path):
            print(f"✓ Optional directory {dir_name} exists")
    
    # Validate Python files
    python_files = [
        '__init__.py',
        '__manifest__.py',
        'models/__init__.py',
        'models/hr_job.py',
    ]
    
    for py_file in python_files:
        filepath = os.path.join(module_path, py_file)
        if os.path.exists(filepath):
            if not validate_python_file(filepath):
                all_valid = False
        else:
            print(f"✗ {py_file} - File missing")
            all_valid = False
    
    # Validate XML files
    xml_files = [
        'views/hr_job_views.xml',
        'demo/hr_job_demo.xml',
    ]
    
    for xml_file in xml_files:
        filepath = os.path.join(module_path, xml_file)
        if os.path.exists(filepath):
            if not validate_xml_file(filepath):
                all_valid = False
        else:
            print(f"✗ {xml_file} - File missing")
            all_valid = False
    
    # Validate manifest specifically
    manifest_path = os.path.join(module_path, '__manifest__.py')
    if not validate_manifest(manifest_path):
        all_valid = False
    
    print("=" * 50)
    if all_valid:
        print("✓ Module validation PASSED - Ready for installation!")
    else:
        print("✗ Module validation FAILED - Please fix the errors above")
    
    return all_valid


if __name__ == '__main__':
    main()
