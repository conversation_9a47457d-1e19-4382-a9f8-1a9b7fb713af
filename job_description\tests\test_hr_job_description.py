# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase


class TestHrJobDescription(TransactionCase):
    
    def setUp(self):
        super(TestHrJobDescription, self).setUp()
        self.HrJob = self.env['hr.job']
        
    def test_job_description_fields_exist(self):
        """Test that the new job description fields exist on hr.job model"""
        job = self.HrJob.create({
            'name': 'Test Job Position',
        })
        
        # Test that the new fields exist and can be set
        job.write({
            'job_objective': 'Test job objective',
            'main_tasks': 'Test main tasks',
            'required_skills': 'Test required skills',
        })
        
        # Verify the fields were set correctly
        self.assertEqual(job.job_objective, 'Test job objective')
        self.assertEqual(job.main_tasks, 'Test main tasks')
        self.assertEqual(job.required_skills, 'Test required skills')
        
    def test_reporting_to_field(self):
        """Test the reporting_to Many2one field"""
        # Create a manager job
        manager_job = self.HrJob.create({
            'name': 'Manager Position',
        })
        
        # Create an employee job that reports to the manager
        employee_job = self.HrJob.create({
            'name': 'Employee Position',
            'reporting_to': manager_job.id,
        })
        
        # Verify the relationship
        self.assertEqual(employee_job.reporting_to.id, manager_job.id)
        self.assertEqual(employee_job.reporting_to.name, 'Manager Position')
        
    def test_job_description_fields_optional(self):
        """Test that job description fields are optional"""
        job = self.HrJob.create({
            'name': 'Test Job Without Description',
        })
        
        # Verify that the job can be created without the description fields
        self.assertTrue(job.id)
        self.assertFalse(job.job_objective)
        self.assertFalse(job.main_tasks)
        self.assertFalse(job.required_skills)
        self.assertFalse(job.reporting_to)
        
    def test_job_description_fields_text_type(self):
        """Test that text fields can handle long content"""
        long_text = "This is a very long text " * 100
        
        job = self.HrJob.create({
            'name': 'Test Job with Long Text',
            'job_objective': long_text,
            'main_tasks': long_text,
            'required_skills': long_text,
        })
        
        # Verify that long text is stored correctly
        self.assertEqual(job.job_objective, long_text)
        self.assertEqual(job.main_tasks, long_text)
        self.assertEqual(job.required_skills, long_text)

    def test_hr_settings_job_description_display(self):
        """Test job description display in HR Settings"""
        # Create a job with job description
        job = self.HrJob.create({
            'name': 'Test Manager Position',
            'job_objective': 'Lead and manage the team effectively',
            'main_tasks': '• Manage team members\n• Set goals and objectives\n• Monitor performance',
            'required_skills': '• Leadership skills\n• Communication skills\n• 5+ years experience',
        })

        # Create settings record
        settings = self.env['res.config.settings'].create({
            'selected_job_position_id': job.id,
        })

        # Test computed fields
        self.assertEqual(settings.display_job_objective, 'Lead and manage the team effectively')
        self.assertEqual(settings.display_main_tasks, '• Manage team members\n• Set goals and objectives\n• Monitor performance')
        self.assertEqual(settings.display_required_skills, '• Leadership skills\n• Communication skills\n• 5+ years experience')
        self.assertEqual(settings.display_job_name, 'Test Manager Position')

    def test_hr_settings_no_job_selected(self):
        """Test HR Settings when no job is selected"""
        settings = self.env['res.config.settings'].create({})

        # Test that fields are empty when no job is selected
        self.assertEqual(settings.display_job_objective, '')
        self.assertEqual(settings.display_main_tasks, '')
        self.assertEqual(settings.display_required_skills, '')
        self.assertEqual(settings.display_reporting_to, '')
        self.assertEqual(settings.display_job_name, '')
        self.assertEqual(settings.display_department, '')
        self.assertEqual(settings.display_employees_count, 0)

    def test_hr_settings_with_reporting_hierarchy(self):
        """Test HR Settings with reporting hierarchy"""
        # Create manager job
        manager_job = self.HrJob.create({
            'name': 'Department Manager',
            'job_objective': 'Manage the department',
        })

        # Create employee job that reports to manager
        employee_job = self.HrJob.create({
            'name': 'Team Member',
            'job_objective': 'Support team activities',
            'reporting_to': manager_job.id,
        })

        # Test settings with employee job
        settings = self.env['res.config.settings'].create({
            'selected_job_position_id': employee_job.id,
        })

        # Verify reporting hierarchy is displayed
        self.assertEqual(settings.display_reporting_to, 'Department Manager')

    def test_hr_settings_empty_values_handling(self):
        """Test HR Settings handles empty/None values correctly"""
        # Create job with minimal data
        job = self.HrJob.create({
            'name': 'Test Job with Empty Values',
            # Leave job description fields empty
        })

        # Create settings record
        settings = self.env['res.config.settings'].create({
            'selected_job_position_id': job.id,
        })

        # Test that empty values are handled correctly (no ValueError)
        self.assertEqual(settings.display_job_name, 'Test Job with Empty Values')
        self.assertEqual(settings.display_employees_count, 0)  # Should be 0, not cause error
        self.assertFalse(settings.display_job_objective)  # Should be False for empty
        self.assertFalse(settings.display_main_tasks)
        self.assertFalse(settings.display_required_skills)

    def test_hr_settings_none_values_handling(self):
        """Test HR Settings handles None values correctly"""
        # Create job and manually set some fields to None to test edge cases
        job = self.HrJob.create({
            'name': 'Test Job with None Values',
        })

        # Create settings record
        settings = self.env['res.config.settings'].create({
            'selected_job_position_id': job.id,
        })

        # Force computation to ensure no errors
        settings._compute_job_description_fields()

        # Should not raise any errors and should have safe default values
        self.assertTrue(isinstance(settings.display_employees_count, int))
        self.assertEqual(settings.display_employees_count, 0)

    def test_hr_settings_valueerror_fix(self):
        """Test specific fix for ValueError: invalid literal for int() with base 10: ''"""
        # Create a job position
        job = self.HrJob.create({
            'name': 'Test Job for ValueError Fix',
            'job_objective': 'Test objective',
        })

        # Manually set no_of_employee to problematic values that could cause ValueError
        problematic_values = ['', None, False, 'invalid', '  ', 0, '0', 0.0]

        for problematic_value in problematic_values:
            with self.subTest(value=problematic_value):
                # Simulate the problematic value
                job.write({'no_of_recruitment': 0})  # This affects computed field

                # Create settings record
                settings = self.env['res.config.settings'].create({
                    'selected_job_position_id': job.id,
                })

                # This should NOT raise ValueError
                try:
                    settings._compute_job_description_fields()
                    # Should always be an integer, never raise ValueError
                    self.assertTrue(isinstance(settings.display_employees_count, int))
                    self.assertGreaterEqual(settings.display_employees_count, 0)
                except ValueError as e:
                    self.fail(f"ValueError raised with value {problematic_value}: {str(e)}")
                except Exception as e:
                    # Other exceptions are logged but shouldn't break the test
                    import logging
                    logging.getLogger(__name__).warning(f"Non-critical error with value {problematic_value}: {str(e)}")
                    # Should still have safe default
                    self.assertEqual(settings.display_employees_count, 0)
