# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase


class TestHrJobDescription(TransactionCase):
    
    def setUp(self):
        super(TestHrJobDescription, self).setUp()
        self.HrJob = self.env['hr.job']
        
    def test_job_description_fields_exist(self):
        """Test that the new job description fields exist on hr.job model"""
        job = self.HrJob.create({
            'name': 'Test Job Position',
        })
        
        # Test that the new fields exist and can be set
        job.write({
            'job_objective': 'Test job objective',
            'main_tasks': 'Test main tasks',
            'required_skills': 'Test required skills',
        })
        
        # Verify the fields were set correctly
        self.assertEqual(job.job_objective, 'Test job objective')
        self.assertEqual(job.main_tasks, 'Test main tasks')
        self.assertEqual(job.required_skills, 'Test required skills')
        
    def test_reporting_to_field(self):
        """Test the reporting_to Many2one field"""
        # Create a manager job
        manager_job = self.HrJob.create({
            'name': 'Manager Position',
        })
        
        # Create an employee job that reports to the manager
        employee_job = self.HrJob.create({
            'name': 'Employee Position',
            'reporting_to': manager_job.id,
        })
        
        # Verify the relationship
        self.assertEqual(employee_job.reporting_to.id, manager_job.id)
        self.assertEqual(employee_job.reporting_to.name, 'Manager Position')
        
    def test_job_description_fields_optional(self):
        """Test that job description fields are optional"""
        job = self.HrJob.create({
            'name': 'Test Job Without Description',
        })
        
        # Verify that the job can be created without the description fields
        self.assertTrue(job.id)
        self.assertFalse(job.job_objective)
        self.assertFalse(job.main_tasks)
        self.assertFalse(job.required_skills)
        self.assertFalse(job.reporting_to)
        
    def test_job_description_fields_text_type(self):
        """Test that text fields can handle long content"""
        long_text = "This is a very long text " * 100
        
        job = self.HrJob.create({
            'name': 'Test Job with Long Text',
            'job_objective': long_text,
            'main_tasks': long_text,
            'required_skills': long_text,
        })
        
        # Verify that long text is stored correctly
        self.assertEqual(job.job_objective, long_text)
        self.assertEqual(job.main_tasks, long_text)
        self.assertEqual(job.required_skills, long_text)
