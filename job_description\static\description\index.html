<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Job Description Module</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            color: #333;
        }
        .header {
            background: #875A7B;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .feature {
            background: #f4f4f4;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #875A7B;
        }
        .screenshot {
            text-align: center;
            margin: 20px 0;
        }
        .installation {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Job Description Module</h1>
        <p>Enhanced Job Description for HR Job Positions in Odoo 15</p>
    </div>

    <h2>Overview</h2>
    <p>This module extends the HR Job Position model to include detailed job description fields, making it easier to manage and document job requirements and responsibilities.</p>

    <h2>Features</h2>
    
    <div class="feature">
        <h3>🎯 Job Objective</h3>
        <p>Define the general objective and purpose of each job position with a dedicated text field.</p>
    </div>

    <div class="feature">
        <h3>📋 Main Tasks</h3>
        <p>List the main tasks and responsibilities for each job position in detail.</p>
    </div>

    <div class="feature">
        <h3>👥 Reporting Hierarchy</h3>
        <p>Establish clear reporting relationships by linking job positions to their supervisory roles.</p>
    </div>

    <div class="feature">
        <h3>🎓 Required Skills</h3>
        <p>Document the required skills, qualifications, and experience needed for each position.</p>
    </div>

    <h2>User Interface</h2>
    <p>All new fields are organized in a dedicated <strong>"Job Description"</strong> tab within the Job Position form, providing a clean and intuitive interface for HR professionals.</p>

    <div class="installation">
        <h2>Installation</h2>
        <ol>
            <li>Copy the module to your Odoo addons directory</li>
            <li>Update the apps list</li>
            <li>Install the "Job Description" module</li>
            <li>Navigate to Employees > Configuration > Job Positions</li>
            <li>Open any job position to see the new "Job Description" tab</li>
        </ol>
    </div>

    <h2>Compatibility</h2>
    <ul>
        <li>Odoo 15.0 Community Edition</li>
        <li>Requires HR module</li>
        <li>Compatible with other HR modules</li>
    </ul>

    <h2>Support</h2>
    <p>For technical support and documentation, please refer to the README.md file included with the module.</p>

    <div style="text-align: center; margin-top: 30px; color: #666;">
        <p>Developed by Custom Development Team</p>
    </div>
</body>
</html>
