# -*- coding: utf-8 -*-
{
    'name': 'Job Description',
    'version': '********.2',
    'category': 'Human Resources',
    'summary': 'Enhanced Job Description for HR Job Positions',
    'description': """
Job Description Module
======================

This module extends the HR Job Position model to include detailed job description fields:

Features:
---------
* Job Objective: Long text field for describing the general objective of the job
* Main Tasks: Long text field for listing the main tasks and responsibilities
* Reporting To: Many2one field linking to another hr.job to show reporting hierarchy
* Required Skills: Long text field for describing the required skills and qualifications

The module adds a new "Job Description" tab to the Job Position form view with all these fields.

Installation:
-------------
1. Install the module
2. Go to Employees > Configuration > Job Positions
3. Open any job position to see the new "Job Description" tab

Author: Custom Development
    """,
    'author': 'Custom Development',
    'website': '',
    'depends': ['hr'],
    'data': [
        'security/ir.model.access.csv',
        'views/hr_job_views.xml',
        'views/res_config_settings_views.xml',
    ],
    'qweb': [],
    'demo': [
        'demo/hr_job_demo.xml',
    ],
    'test': [
        'tests/test_hr_job_description.py',
    ],
    'assets': {
        'web.assets_backend': [
            'job_description/static/src/scss/job_description.scss',
            'job_description/static/src/js/job_description_settings.js',
        ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
