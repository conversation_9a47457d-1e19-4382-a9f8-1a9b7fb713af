<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Inherit HR Settings to add Job Description Display -->
        <record id="res_config_settings_view_form_inherit_job_description" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.job.description</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="hr.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='employees_setting_container']" position="after">
                    <div class="row mt16 o_settings_container" name="job_description_container">
                        <div class="col-12 o_setting_box" id="job_description_display_setting">
                            <div class="o_setting_left_pane">
                                <field name="selected_job_position_id" invisible="1"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label string="Job Position Details" for="selected_job_position_id"/>
                                <div class="text-muted">
                                    View detailed job descriptions for any position
                                </div>
                                <div class="content-group mt16">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <label for="selected_job_position_id" string="Select Job Position"/>
                                            <field name="selected_job_position_id" 
                                                   placeholder="Choose a job position to view details..."
                                                   options="{'no_create': True, 'no_create_edit': True}"
                                                   class="o_field_many2one"/>
                                        </div>
                                    </div>
                                    
                                    <!-- Job Description Display Section -->
                                    <div class="mt16" attrs="{'invisible': [('selected_job_position_id', '=', False)]}">
                                        <div class="alert alert-info" role="alert">
                                            <strong>Job Position Information</strong><br/>
                                            <span class="text-muted">Detailed information for the selected job position (Read-only)</span>
                                        </div>
                                        
                                        <!-- Basic Job Info -->
                                        <div class="row mb16">
                                            <div class="col-lg-6">
                                                <label for="display_job_name" class="o_form_label">Position Name</label>
                                                <field name="display_job_name" readonly="1" class="o_field_char"/>
                                            </div>
                                            <div class="col-lg-6">
                                                <label for="display_department" class="o_form_label">Department</label>
                                                <field name="display_department" readonly="1" class="o_field_char"/>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb16">
                                            <div class="col-lg-6">
                                                <label for="display_reporting_to" class="o_form_label">Reporting To</label>
                                                <field name="display_reporting_to" readonly="1" class="o_field_char"/>
                                            </div>
                                            <div class="col-lg-6">
                                                <label for="display_employees_count" class="o_form_label">Current Employees</label>
                                                <field name="display_employees_count" readonly="1" class="o_field_integer"/>
                                            </div>
                                        </div>
                                        
                                        <!-- Job Description Details -->
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="o_group">
                                                    <div class="o_group_col_6">
                                                        <div class="o_field_widget">
                                                            <label for="display_job_objective" class="o_form_label">Job Objective</label>
                                                            <field name="display_job_objective" 
                                                                   readonly="1" 
                                                                   widget="text"
                                                                   class="o_field_text job_description_readonly"/>
                                                        </div>
                                                        
                                                        <div class="o_field_widget mt16">
                                                            <label for="display_main_tasks" class="o_form_label">Main Tasks</label>
                                                            <field name="display_main_tasks" 
                                                                   readonly="1" 
                                                                   widget="text"
                                                                   class="o_field_text job_description_readonly"/>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="o_group_col_6">
                                                        <div class="o_field_widget">
                                                            <label for="display_required_skills" class="o_form_label">Required Skills</label>
                                                            <field name="display_required_skills" 
                                                                   readonly="1" 
                                                                   widget="text"
                                                                   class="o_field_text job_description_readonly"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Action Buttons -->
                                        <div class="mt16">
                                            <button name="%(hr.action_hr_job)d" 
                                                    type="action" 
                                                    string="Edit Job Position" 
                                                    class="btn-link"
                                                    icon="fa-edit"
                                                    context="{'default_id': selected_job_position_id}"/>
                                            <button name="%(hr.action_hr_job)d" 
                                                    type="action" 
                                                    string="View All Job Positions" 
                                                    class="btn-link"
                                                    icon="fa-list"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
