/** @odoo-module **/

import { registry } from "@web/core/registry";
import { FormController } from "@web/views/form/form_controller";
import { patch } from "@web/core/utils/patch";

/**
 * Extend FormController to handle job description display in HR Settings
 */
patch(FormController.prototype, "job_description_settings", {
    
    /**
     * Override setup to add custom behavior for job description settings
     */
    setup() {
        this._super(...arguments);
        
        // Check if this is the HR settings form
        if (this.props.resModel === 'res.config.settings') {
            this._setupJobDescriptionHandlers();
        }
    },
    
    /**
     * Setup handlers for job description functionality
     */
    _setupJobDescriptionHandlers() {
        // Add event listener for job position selection changes
        this.env.bus.addEventListener('field_changed:selected_job_position_id', this._onJobPositionChanged.bind(this));
    },
    
    /**
     * Handle job position selection change
     */
    async _onJobPositionChanged(event) {
        const { record, fieldName, value } = event.detail;

        if (fieldName === 'selected_job_position_id' && record.resModel === 'res.config.settings') {
            try {
                // Add visual feedback
                this._showJobDescriptionLoadingState(true);

                // Trigger recomputation of dependent fields with safe handling
                await record.update({
                    selected_job_position_id: value,
                });

                // Remove loading state
                this._showJobDescriptionLoadingState(false);

            } catch (error) {
                console.error('Error updating job position selection:', error);
                this._showJobDescriptionLoadingState(false);

                // Show user-friendly error message
                this.env.services.notification.add(
                    'Error loading job description. Please try again.',
                    { type: 'warning' }
                );
            }
        }
    },
    
    /**
     * Show loading state for job description fields
     */
    _showJobDescriptionLoadingState(isLoading) {
        const jobDescriptionSection = document.querySelector('#job_description_display_setting');
        if (jobDescriptionSection) {
            const readonlyFields = jobDescriptionSection.querySelectorAll('.job_description_readonly textarea');
            
            readonlyFields.forEach(field => {
                if (isLoading) {
                    field.style.opacity = '0.6';
                    field.placeholder = 'Loading job description...';
                } else {
                    field.style.opacity = '1';
                    field.placeholder = '';
                }
            });
        }
    }
});

/**
 * Custom widget for job description display in settings
 */
class JobDescriptionDisplayWidget extends owl.Component {
    static template = "job_description.JobDescriptionDisplay";
    
    setup() {
        this.state = useState({
            isVisible: false,
            jobData: {}
        });
        
        // Watch for changes in job position selection
        useEffect(() => {
            if (this.props.record.data.selected_job_position_id) {
                this.state.isVisible = true;
                this._loadJobDescription();
            } else {
                this.state.isVisible = false;
            }
        }, () => [this.props.record.data.selected_job_position_id]);
    }
    
    async _loadJobDescription() {
        const jobId = this.props.record.data.selected_job_position_id;
        if (jobId) {
            try {
                // The data will be automatically loaded through the computed fields
                // This is just for any additional processing if needed
                this.state.jobData = {
                    objective: this.props.record.data.display_job_objective,
                    tasks: this.props.record.data.display_main_tasks,
                    skills: this.props.record.data.display_required_skills,
                    reporting: this.props.record.data.display_reporting_to
                };
            } catch (error) {
                console.error('Error loading job description:', error);
            }
        }
    }
    
    _onEditJobPosition() {
        const jobId = this.props.record.data.selected_job_position_id;
        if (jobId) {
            this.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: 'hr.job',
                res_id: jobId[0],
                views: [[false, 'form']],
                target: 'current',
            });
        }
    }
    
    _onViewAllPositions() {
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'hr.job',
            views: [[false, 'tree'], [false, 'form']],
            target: 'current',
        });
    }
}

JobDescriptionDisplayWidget.components = {};

// Register the widget
registry.category("fields").add("job_description_display", JobDescriptionDisplayWidget);

/**
 * Utility functions for job description handling
 */
const JobDescriptionUtils = {
    
    /**
     * Format job description text for display
     */
    formatJobDescription(text) {
        if (!text) return 'Not specified';
        
        // Convert bullet points to HTML list if needed
        if (text.includes('•') || text.includes('*')) {
            const lines = text.split('\n');
            let formattedText = '';
            let inList = false;
            
            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (trimmedLine.startsWith('•') || trimmedLine.startsWith('*')) {
                    if (!inList) {
                        formattedText += '<ul>';
                        inList = true;
                    }
                    formattedText += `<li>${trimmedLine.substring(1).trim()}</li>`;
                } else if (trimmedLine) {
                    if (inList) {
                        formattedText += '</ul>';
                        inList = false;
                    }
                    formattedText += `<p>${trimmedLine}</p>`;
                }
            });
            
            if (inList) {
                formattedText += '</ul>';
            }
            
            return formattedText;
        }
        
        return text.replace(/\n/g, '<br>');
    },
    
    /**
     * Validate job description completeness
     */
    validateJobDescription(jobData) {
        const requiredFields = ['job_objective', 'main_tasks', 'required_skills'];
        const missingFields = [];
        
        requiredFields.forEach(field => {
            if (!jobData[field] || jobData[field].trim() === '') {
                missingFields.push(field);
            }
        });
        
        return {
            isComplete: missingFields.length === 0,
            missingFields: missingFields
        };
    }
};

// Export utilities for use in other modules
export { JobDescriptionUtils };
